<template>
  <div class="person-detail-popup-overlay">
    <div class="person-detail-container">
      <!-- 头部区域 -->
      <div class="person-header">
        <!-- 人物名称显示区域 -->
        <div class="person-name-section">
          <div class="person-name">{{ personDetail?.canonical_name || personName }}</div>
        </div>

        <!-- 头像、按钮、关闭按钮包装区域 -->
        <div class="header-controls-wrapper">
          <!-- 左上角头像 -->
          <div class="avatar-section">
            <img
              :src="getAvatarUrl(personDetail?.avatar, personDetail?.person_id)"
              alt="头像"
              class="person-avatar clickable-avatar"
              @click="handleAvatarClick"
            />
          </div>

          <!-- 中间按钮区域 -->
          <div class="header-action-buttons">
            <!-- Tab切换按钮 -->
            <div class="tab-switch" @click="toggleTab">
              <div class="tab-slider" :class="{ 'slide-right': activeTab === 'chat' }"></div>
              <div class="tab-option" :class="{ active: activeTab === 'info' }">个人信息</div>
              <div class="tab-option" :class="{ active: activeTab === 'chat' }">AI问答</div>
            </div>
          </div>

          <!-- 右上角关闭按钮 -->
          <div class="close-section">
            <button class="close-btn" @click="$emit('close')">
              <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
            </button>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-sections">
        <!-- 个人信息Tab内容 -->
        <div v-if="activeTab === 'info'" class="info-tab-content">
          <!-- 基础信息 -->
          <div class="info-section">
            <div class="section-header">
              <span class="section-icon">📋</span>
              <span class="section-title">基础信息</span>
              <div class="section-actions">
                <button class="section-mic-btn" title="语音" @click="handleBasicInfoMicClick">
                  <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
                </button>
                <button class="section-edit-btn" title="编辑" @click="handleSectionArrowClick">
                  <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
                </button>
              </div>
            </div>
            <div class="section-content">
              <div v-if="loading" class="loading-text">加载中...</div>
              <div v-else-if="personDetail" class="info-content">
                <!-- 编辑模式 -->
                <div v-if="isEditingBasicInfo" class="edit-mode">
                  <textarea
                    v-model="basicInfoEditValue"
                    class="edit-textarea"
                    placeholder="请输入基础信息..."
                    rows="4"
                    @blur="handleBasicInfoEditComplete"
                    @keydown.esc="handleBasicInfoEditCancel"
                  />
                </div>
                <!-- 显示模式 -->
                <div
                  v-else
                  class="info-text-container"
                  :class="{ expanded: isInfoExpanded }"
                  @mousedown="handleMouseDown"
                  @mouseup="handleMouseUp"
                >
                  <p class="info-text" :class="{ collapsed: !isInfoExpanded && shouldShowExpansion }">
                    {{ personDetail.profile_summary || '暂无基础信息' }}
                  </p>
                </div>
              </div>
              <div v-else class="info-content">
                <div class="info-text-container">
                  <p class="info-text">暂无基础信息</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 提醒事项 -->
          <div class="reminder-section">
            <div class="section-header">
              <span class="section-icon">⏰</span>
              <span class="section-title">提醒事项</span>
              <button class="add-reminder-btn" @click="handleAddReminder">
                <span class="add-icon">+</span>
                添加提醒
              </button>
            </div>
            <div class="section-content">
              <div v-if="loadingReminders" class="loading-text">加载中...</div>
              <div v-else-if="reminders.length === 0" class="reminder-content">
                <div class="empty-reminder">快来添加提醒事项！</div>
              </div>
              <div v-else class="reminder-list">
                <div v-for="reminder in reminders" :key="reminder.reminder_id" class="reminder-item">
                  <div class="reminder-info">
                    <div class="reminder-text">{{ reminder.display_text || '提醒事项' }}</div>
                  </div>
                  <div class="reminder-actions">
                    <button class="edit-reminder-btn" @click="handleEditReminder(reminder)">
                      <span class="edit-icon">✏️</span>
                    </button>
                    <button class="delete-reminder-btn" @click="handleDeleteReminder(reminder)">
                      <span class="delete-icon">×</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 天气分析 -->
          <div class="weather-section">
            <div class="section-header">
              <span class="section-icon">☀️</span>
              <span class="section-title">天气分析</span>
              <div class="section-actions">
                <button class="section-mic-btn" title="语音" @click="handleWeatherMicClick">
                  <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
                </button>
                <button class="section-edit-btn" title="编辑" @click="handleWeatherSectionArrowClick">
                  <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
                </button>
              </div>
            </div>
            <div class="section-content">
              <!-- 编辑模式 -->
              <div v-if="showWeatherEdit" class="edit-attribute-container">
                <div class="add-attribute-container">
                  <input
                    v-model="weatherEditValue"
                    type="text"
                    class="attribute-value"
                    placeholder="请输入当前城市"
                    @blur="handleWeatherEditComplete"
                    @keydown.enter="handleWeatherEditComplete"
                    @keydown.esc="handleWeatherEditCancel"
                  />
                </div>
              </div>
              <!-- 显示模式 -->
              <div v-else>
                <div v-if="loadingWeather" class="loading-text">加载中...</div>
                <div v-else-if="weatherData && weatherData.result === 'success'" class="weather-content">
                  <!-- 显示当前城市的个性化提醒 -->
                  <div v-if="currentCityWeatherData" class="weather-reminder">
                    {{ currentCityWeatherData.personalized_reminder }}
                  </div>
                  <div v-else class="weather-suggestion">还没有关于ta的地点信息哦～快去和老董聊聊吧！</div>
                </div>
                <div v-else-if="weatherData && weatherData.result === 'error'" class="weather-content">
                  <div class="weather-suggestion">还没有关于ta的地点信息哦～快去和老董聊聊吧！</div>
                </div>
                <div v-else-if="weatherData === null" class="weather-content">
                  <div class="weather-error">
                    <div class="error-title">⚠️ 天气信息获取失败</div>
                    <div class="error-message">网络请求超时或服务异常</div>
                    <div class="error-suggestion">💡 建议：请检查网络连接或稍后重试</div>
                  </div>
                </div>
                <div v-else class="weather-content">
                  <div class="weather-suggestion">还没有关于ta的地点信息哦～快去和老董聊聊吧！</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 推荐话题 -->
          <div class="topic-section">
            <div class="section-header">
              <span class="section-icon">💬</span>
              <span class="section-title">推荐话题</span>
              <button class="refresh-btn" title="刷新话题" :disabled="loadingTopics" @click="handleRefreshTopics">
                <img src="@/assets/icon/refresh.svg" alt="刷新" class="refresh-icon" />
              </button>
            </div>
            <div class="section-content">
              <div v-if="loadingTopics" class="loading-text">加载中...</div>
              <div v-else-if="recommendedTopics.length > 0" class="topic-content">
                <div v-for="topic in recommendedTopics" :key="topic.topic" class="topic-item">• {{ topic.topic }}</div>
              </div>
              <div v-else class="topic-content">
                <div class="empty-topic">暂无推荐话题，点击刷新按钮获取推荐</div>
              </div>
            </div>
          </div>

          <!-- 旅行记录 -->
          <div class="travel-section">
            <div class="section-header">
              <span class="section-icon">✈️</span>
              <span class="section-title">旅行记录</span>
              <div class="section-actions">
                <button class="section-mic-btn" title="语音" @click="handleTravelMicClick">
                  <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
                </button>
                <button class="section-edit-btn" title="编辑" @click="handleTravelSectionArrowClick">
                  <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
                </button>
              </div>
            </div>
            <div class="section-content">
              <!-- 编辑模式 -->
              <div v-if="showTravelEdit" class="edit-attribute-container">
                <div class="add-attribute-container">
                  <input
                    v-model="travelEditValue"
                    type="text"
                    class="attribute-value"
                    placeholder="请输入旅游历史"
                    @blur="handleTravelEditComplete"
                    @keydown.enter="handleTravelEditComplete"
                    @keydown.esc="handleTravelEditCancel"
                  />
                </div>
              </div>
              <!-- 显示模式 -->
              <div v-else class="travel-content">
                <div v-if="getTravelHistory()" class="travel-info">
                  <div v-for="(location, index) in getTravelHistoryList()" :key="index" class="travel-item">
                    {{ location }}
                  </div>
                </div>
                <div v-else class="empty-travel">记录每一次旅行足迹，分享美好旅途回忆。</div>
              </div>
            </div>
          </div>

          <!-- 饮食偏好 -->
          <div class="food-section">
            <div class="section-header">
              <span class="section-icon">🍽️</span>
              <span class="section-title">饮食偏好</span>
              <div class="section-actions">
                <button class="section-mic-btn" title="语音" @click="handleFoodMicClick">
                  <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
                </button>
                <button class="section-edit-btn" title="编辑" @click="handleFoodSectionArrowClick">
                  <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
                </button>
              </div>
            </div>
            <div class="section-content">
              <!-- 编辑模式 -->
              <div v-if="showFoodEdit" class="edit-attribute-container">
                <div class="add-attribute-container">
                  <input
                    v-model="foodEditValue"
                    type="text"
                    class="attribute-value"
                    placeholder="请输入餐饮偏好"
                    @blur="handleFoodEditComplete"
                    @keydown.enter="handleFoodEditComplete"
                    @keydown.esc="handleFoodEditCancel"
                  />
                </div>
              </div>
              <!-- 显示模式 -->
              <div v-else class="food-content">
                <div v-if="getFoodPreference()" class="food-info">
                  <div v-for="(preference, index) in getFoodPreferenceList()" :key="index" class="food-item">
                    {{ preference }}
                  </div>
                </div>
                <div v-else class="empty-food">了解TA的口味偏好，为下次聚餐做好准备。</div>
              </div>
            </div>
          </div>

          <!-- 我的期望 -->
          <div class="expectation-section">
            <div class="section-header">
              <span class="section-icon">🌟</span>
              <span class="section-title">我的期望</span>
              <div class="section-actions">
                <button class="section-mic-btn" title="语音" @click="handleExpectationMicClick">
                  <img src="@/assets/icon/mic.png" alt="语音" class="section-mic-icon" />
                </button>
                <button class="section-edit-btn" title="编辑" @click="handleExpectationSectionArrowClick">
                  <img src="@/assets/icon/edit.png" alt="编辑" class="section-edit-icon" />
                </button>
              </div>
            </div>
            <div class="section-content">
              <!-- 编辑模式 -->
              <div v-if="showExpectationEdit" class="edit-attribute-container">
                <div class="add-attribute-container">
                  <input
                    v-model="expectationEditValue"
                    type="text"
                    class="attribute-value"
                    placeholder="请输入期望"
                    @blur="handleExpectationEditComplete"
                    @keydown.enter="handleExpectationEditComplete"
                    @keydown.esc="handleExpectationEditCancel"
                  />
                </div>
              </div>
              <!-- 显示模式 -->
              <div v-else class="expectation-content">
                <div v-if="getExpectation()" class="expectation-info">{{ getExpectation() }}</div>
                <div v-else class="empty-expectation">记录你的期望和心愿。</div>
              </div>
            </div>
          </div>

          <!-- 底部操作区域 - 移到内容区域最底部 -->
          <div v-if="!isUserProfile" class="person-footer-in-content">
            <button class="delete-person-btn" title="删除人员" @click="handleDeletePerson">
              <img src="@/assets/icon/delete.png" alt="删除" class="delete-icon" />
              <span class="delete-text">删除联系人</span>
            </button>
          </div>
        </div>

        <!-- AI问答Tab内容 -->
        <div v-else-if="activeTab === 'chat'" class="chat-tab-content voice-chat-dialog">
          <!-- 人员信息显示区域 -->
          <div class="info-display-section">
            <div class="section-header">
              <span class="section-icon">👤</span>
              <span class="section-title">当前信息</span>
            </div>
            <div class="section-content">
              <div v-if="loading" class="loading-text">加载中...</div>
              <div v-else-if="personDetail" class="info-display-content">
                <!-- 基础信息 -->
                <div class="info-item">
                  <span class="info-label">基础信息:</span>
                  <span class="info-value">{{ personDetail.profile_summary || '暂无' }}</span>
                </div>
                <!-- 关键属性 -->
                <div v-if="Object.keys(processedKeyAttributes).length > 0" class="attributes-display">
                  <div v-for="(value, key) in processedKeyAttributes" :key="key" class="info-item">
                    <span class="info-label">{{ key }}:</span>
                    <span class="info-value">{{ value }}</span>
                  </div>
                </div>
                <!-- 提醒数量 -->
                <div class="info-item">
                  <span class="info-label">提醒事项:</span>
                  <span class="info-value">{{ reminders.length }}条</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 语音对话区域 -->
          <div class="voice-chat-section">
            <!-- 聊天消息列表 -->
            <div ref="chatMessagesRef" class="chat-messages">
              <div v-for="message in chatMessages" :key="message.key" class="chat-message" :class="message.role">
                <div
                  class="message-content"
                  :class="{ 'loading-content': !message.isFinish && message.role === 'assistant' }"
                >
                  <!-- 显示消息内容 -->
                  <div v-if="message.content || message.isFinish">{{ message.content }}</div>
                  <!-- 显示loading动画 -->
                  <div v-else-if="!message.isFinish && message.role === 'assistant'" class="loading">
                    <div class="dot"></div>
                    <div class="dot"></div>
                    <div class="dot"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 语音输入区域 -->
            <div class="voice-input-area">
              <!-- 识别文字显示区域 -->
              <div v-if="recognizedText" class="recognized-text">
                {{ recognizedText }}
              </div>

              <!-- 语音输入模式 -->
              <div v-if="!isKeyboardMode" class="voice-input-mode">
                <div class="voice-prompt-text">解答与{{ personName }}有关的问题</div>
                <button class="voice-btn" :class="{ recording: isRecording }" @click="toggleVoiceRecording">
                  <div class="voice-button-bg" :class="{ recording: isRecording }"></div>
                  <img src="@/assets/icon/mic.png" alt="语音" class="voice-mic-icon" />
                </button>

                <button class="keyboard-toggle-btn" @click="toggleInputMode">
                  <span class="chevron-right">></span>
                </button>
              </div>

              <!-- 键盘输入模式 -->
              <div v-if="isKeyboardMode" class="keyboard-input-mode">
                <button class="voice-toggle-btn-circle" @click="toggleInputMode">
                  <div class="voice-button-bg"></div>
                  <img src="@/assets/icon/mic.png" alt="切换语音" class="voice-mic-icon" />
                </button>

                <div class="text-input-container">
                  <input
                    v-model="chatInput"
                    type="text"
                    :placeholder="`解答与${personName}有关的内容`"
                    class="text-input"
                    :disabled="chatStore.answerStatus === AnswerStatusEnum.LOADING"
                    @keydown.enter="handleSendChatMessage"
                  />
                </div>

                <button
                  class="send-btn-circle"
                  :class="{
                    'not-input': !chatInput.trim() || chatStore.answerStatus === AnswerStatusEnum.LOADING,
                  }"
                  @click="handleSendChatMessage"
                >
                  <i class="iconfont icon-mobile-send" class-prefix="icon"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <div v-if="showDeleteDialog" class="dialog-overlay">
      <div class="dialog-container">
        <div class="dialog-header">
          <div class="dialog-title">确认删除</div>
          <div class="dialog-close" @click="closeDeleteDialog">
            <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
          </div>
        </div>
        <div class="dialog-content">
          <div class="delete-warning">
            确定要删除 <strong>{{ personDetail?.canonical_name }}</strong> 吗？
          </div>
          <div class="delete-hint">删除后将无法恢复相关的记忆数据</div>
        </div>
        <div class="dialog-footer">
          <button class="cancel-btn" @click="closeDeleteDialog">取消</button>
          <button class="delete-confirm-btn" :disabled="isDeleting" @click="confirmDeletePerson">
            {{ isDeleting ? '删除中...' : '确认删除' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 编辑人员弹窗 -->
    <PersonEditDialog
      v-if="showEditDialog"
      :person="editPersonData"
      :user-id="userId"
      :is-user-profile="isUserProfile"
      @close="handleCloseEditDialog"
      @success="handleEditSuccess"
    />

    <!-- 头像选择弹窗 -->
    <AvatarSelectionDialog
      v-if="showAvatarSelectionDialog"
      @close="showAvatarSelectionDialog = false"
      @select-avatar="handleAvatarSelect"
      @upload-avatar="handleUploadAvatar"
    />

    <!-- 隐藏的头像上传组件 -->
    <div style="display: none">
      <AvatarUpload
        ref="avatarUploadRef"
        v-model="hiddenAvatarValue"
        :size="50"
        placeholder="上传头像"
        :max-size="10"
        @upload-success="handleAvatarUploadSuccess"
        @upload-error="handleAvatarUploadError"
      />
    </div>

    <!-- 添加提醒弹窗 -->
    <AddReminderDialog
      :show="showAddReminderDialog"
      :user-id="userId"
      :person-id="personId"
      @close="showAddReminderDialog = false"
      @success="handleAddReminderSuccess"
    />

    <!-- 编辑提醒弹窗 -->
    <AddReminderDialog
      :show="showEditReminderDialog"
      :user-id="userId"
      :person-id="personId"
      :edit-reminder="reminderToEdit"
      @close="showEditReminderDialog = false"
      @success="handleEditReminderSuccess"
    />

    <!-- 删除提醒确认弹窗 -->
    <div v-if="showDeleteReminderDialog" class="dialog-overlay">
      <div class="dialog-container">
        <div class="dialog-header">
          <div class="dialog-title">确认删除</div>
          <div class="dialog-close" @click="closeDeleteReminderDialog">
            <img src="@/assets/icon/close.png" alt="关闭" class="close-icon" />
          </div>
        </div>
        <div class="dialog-content">
          <div class="delete-warning">确定要删除这个提醒事项吗？</div>
          <div v-if="reminderToDelete" class="delete-hint">
            {{ reminderToDelete.display_text || '提醒事项' }}
          </div>
        </div>
        <div class="dialog-footer">
          <button class="cancel-btn" @click="closeDeleteReminderDialog">取消</button>
          <button class="delete-confirm-btn" @click="confirmDeleteReminder">确认删除</button>
        </div>
      </div>
    </div>

    <!-- 语音聊天弹窗 -->
    <VoiceChatDialog
      v-if="showVoiceChatDialog"
      :section-info="voiceChatSectionInfo"
      :person-name="personName"
      :user-id="userId"
      @close="handleVoiceChatDialogClose"
      @chat-complete="handleVoiceChatComplete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick, computed, watch } from 'vue';
import {
  getPersonDetail,
  getPersonWeather,
  getReminders,
  deleteReminder,
  getRecommendTopics,
  type IPersonDetail,
  type IGetPersonWeatherResponse,
  type IGetPersonDetailResponse,
  type IReminder,
  type IRecommendedTopic,
} from '@/apis/memory';
import {
  type IPersonData,
  deletePerson,
  getUserProfile,
  updatePerson,
  type IGetUserProfileResponse,
} from '@/apis/relation';
import { showFailToast, showSuccessToast, showToast } from 'vant';
import { streamChat, createConversation, type IToolCall, getStreamAsr } from '@/apis/chat';
import { Typewriter } from '@/utils/typeWriter';
import {
  refreshWeatherAfterPersonUpdate,
  weatherRefreshManager,
  type IWeatherRefreshEvent,
} from '@/utils/weatherRefresh';
import { useChatStore } from '@/stores/chat';
import { AnswerStatusEnum } from '@/constants/chat';
import { getAvatarUrl } from '@/utils/avatarUtils';
import { generateRandomString } from '@/utils';
import { debounce } from 'lodash-es';
import Recorder from 'recorder-realtime';
import AvatarSelectionDialog from '@/components/AvatarSelectionDialog.vue';
import AvatarUpload from '@/components/AvatarUpload.vue';
import AddReminderDialog from './AddReminderDialog.vue';
import PersonEditDialog from './PersonEditDialog.vue';
import VoiceChatDialog from './VoiceChatDialog.vue';

// 聊天消息类型定义
interface IChatStreamContent {
  role: 'user' | 'assistant';
  content: string;
  key: number | string;
  isFinish: boolean;
}

// Props定义
interface IProps {
  userId: string;
  personId: string;
  personName: string;
  isUserProfile?: boolean; // 新增：标识是否为用户档案
}

const props = defineProps<IProps>();

// Emits定义
const emit = defineEmits<{
  close: [];
  refresh: [];
}>();

// 响应式数据
const loading = ref(true);
const loadingWeather = ref(true);
const personDetail = ref<IPersonDetail | null>(null);
const weatherData = ref<IGetPersonWeatherResponse | null>(null);
const isInfoExpanded = ref(false);
const shouldShowExpansion = ref(false);

// 提醒相关
const loadingReminders = ref(false);
const reminders = ref<IReminder[]>([]);
const showAddReminderDialog = ref(false);
const showEditReminderDialog = ref(false);
const showDeleteReminderDialog = ref(false);
const reminderToDelete = ref<IReminder | null>(null);
const reminderToEdit = ref<IReminder | null>(null);

// 编辑状态相关
const isEditingBasicInfo = ref(false);
const basicInfoEditValue = ref('');
const showWeatherEdit = ref(false);
const weatherEditValue = ref('');
const showTravelEdit = ref(false);
const travelEditValue = ref('');
const showFoodEdit = ref(false);
const foodEditValue = ref('');
const showExpectationEdit = ref(false);
const expectationEditValue = ref('');

// 推荐话题相关
const loadingTopics = ref(false);
const recommendedTopics = ref<IRecommendedTopic[]>([]);

// 计算属性：获取处理后的key_attributes对象
const processedKeyAttributes = computed(() => {
  if (!personDetail.value || !personDetail.value.key_attributes) {
    return {};
  }

  let attributes = personDetail.value.key_attributes;

  // 如果是字符串，尝试解析为对象
  if (typeof attributes === 'string') {
    try {
      attributes = JSON.parse(attributes);
      console.log('✅ [PersonDetailPopup] key_attributes字符串解析成功:', attributes);
    } catch (error) {
      console.warn('⚠️ [PersonDetailPopup] key_attributes字符串解析失败:', error, '原始数据:', attributes);
      return {};
    }
  }

  // 确保是对象类型
  if (typeof attributes !== 'object' || attributes === null) {
    console.warn('⚠️ [PersonDetailPopup] key_attributes不是有效对象:', attributes);
    return {};
  }

  // 过滤掉空值的属性
  const filteredAttributes: Record<string, string> = {};
  Object.entries(attributes).forEach(([key, value]) => {
    if (value && String(value).trim()) {
      filteredAttributes[key] = String(value);
    }
  });

  return filteredAttributes;
});

// 计算属性：获取当前城市的天气数据
const currentCityWeatherData = computed(() => {
  if (!weatherData.value || weatherData.value.result !== 'success' || !weatherData.value.weather_data) {
    return null;
  }

  // 查找当前城市的天气数据
  const attributes = processedKeyAttributes.value;
  const currentCity = attributes['当前城市'];

  if (!currentCity) {
    return null;
  }

  // 在weather_data中查找匹配的城市数据
  const weatherEntries = Object.entries(weatherData.value.weather_data);
  const matchingEntry = weatherEntries.find(
    ([locationKey, cityData]) => locationKey === '当前城市' || cityData.city === currentCity,
  );

  return matchingEntry ? matchingEntry[1] : null;
});

// 获取旅游历史属性值
const getTravelHistory = (): string => {
  const attributes = processedKeyAttributes.value;
  return attributes['旅游历史'] || '';
};

// 获取旅游历史列表（分割后的数组）
const getTravelHistoryList = (): string[] => {
  const travelHistory = getTravelHistory();
  if (!travelHistory) return [];
  return travelHistory
    .split('|')
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

// 获取餐饮偏好属性值
const getFoodPreference = (): string => {
  const attributes = processedKeyAttributes.value;
  return attributes['餐饮偏好'] || '';
};

// 获取餐饮偏好列表（分割后的数组）
const getFoodPreferenceList = (): string[] => {
  const foodPreference = getFoodPreference();
  if (!foodPreference) return [];
  return foodPreference
    .split('|')
    .map((item) => item.trim())
    .filter((item) => item.length > 0);
};

// 获取期望属性值
const getExpectation = (): string => {
  const attributes = processedKeyAttributes.value;
  return attributes['期望'] || '';
};

// 编辑弹窗相关
const showEditDialog = ref(false);
const editPersonData = ref<IPersonData | null>(null);

// 头像选择弹窗相关
const showAvatarSelectionDialog = ref(false);
const avatarUploadRef = ref();
const hiddenAvatarValue = ref('');

// 删除确认弹窗相关
const showDeleteDialog = ref(false);
const isDeleting = ref(false);

// Tab切换相关
const activeTab = ref<'info' | 'chat'>('info');

// 聊天相关
const chatInput = ref('');
const chatMessages = ref<IChatStreamContent[]>([]);
const chatMessagesRef = ref<HTMLElement | null>(null);
const conversationId = ref('');
const streamController = ref<AbortController | null>(null);
const enableMention = ref(false); // @XX 功能开关

// mention选择器的宽度
const mentionWidth = ref(120); // 默认宽度
const mentionSelector = ref<HTMLElement | null>(null);

// 语音录音相关状态
const isRecording = ref(false);
const recognizedText = ref('');
const micPermission = ref(false);
const sessionId = ref('');
const audioBufferIndex = ref(0);
const lastBuffer = ref<ArrayBuffer | null>(null);

// 输入模式状态：false为语音输入模式，true为键盘输入模式
const isKeyboardMode = ref(false);
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let recorder: any = null;
let timerId: ReturnType<typeof setTimeout> | null = null;
let mediaStream: MediaStream | null = null;

// 计算mention选择器的实际宽度
const updateMentionWidth = async () => {
  if (mentionSelector.value) {
    await nextTick(); // 等待DOM更新
    const width = mentionSelector.value.offsetWidth;
    // 设置输入框左边距为mention宽度 + 基础padding(22px) + 间距(8px)
    mentionWidth.value = width > 0 ? width + 22 + 8 : 120; // 如果获取不到宽度，使用默认值
  }
};

// 切换输入模式
const toggleInputMode = () => {
  isKeyboardMode.value = !isKeyboardMode.value;
};

// 释放麦克风资源
const releaseMicrophoneResources = () => {
  if (mediaStream) {
    mediaStream.getTracks().forEach((track) => track.stop());
    mediaStream = null;
  }
};

// 设置麦克风权限
const setMicPermission = async () => {
  try {
    mediaStream = await navigator.mediaDevices.getUserMedia({ audio: true });
    micPermission.value = true;
    if (!recorder) {
      initRecorder();
    }
  } catch (error) {
    micPermission.value = false;
    showToast('请授权麦克风权限~');
  }
};

// 初始化录音机
const initRecorder = () => {
  if (!Recorder.isRecordingSupported()) {
    showToast('录音失败，浏览器不支持录音功能');
    return;
  }

  recorder = new Recorder({
    recordingGain: 1,
    numberOfChannels: 1,
    wavBitDepth: 16,
    format: 'pcm',
    wavSampleRate: 16000,
    streamPages: true,
    bufferLength: 4096,
  });

  recorder.onstart = () => {};

  recorder.onstreamerror = () => {
    showToast('录音失败');
    cancelRecording();
  };

  recorder.ondataavailable = async (data: { command: string; buffer: ArrayBuffer }) => {
    if (data.command === 'buffer') {
      lastBuffer.value = data.buffer;
      audioBufferIndex.value += 1;
      const streamData = await getStreamAsr({
        sessionId: sessionId.value,
        format: 'pcm',
        sampleRate: 16000,
        index: audioBufferIndex.value,
        data: data.buffer,
      });
      if (streamData.data.text) {
        recognizedText.value = streamData.data.full_text;
        await autoSendTimeout();
      }
    }
  };
};

// 两秒不说话自动发送
const autoSendTimeout = debounce(async () => {
  await stopVoiceRecording();
}, 2000);

// 取消录音
const cancelRecording = () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }
  releaseMicrophoneResources();
  recognizedText.value = '';
};

// 切换语音录音状态
const toggleVoiceRecording = async () => {
  if (isRecording.value) {
    // 如果正在录音，停止录音
    await stopVoiceRecording();
  } else {
    // 如果没有录音，开始录音
    await startVoiceRecording();
  }
};

// 开始语音录音
const startVoiceRecording = async () => {
  // 如果没有麦克风权限，先请求权限
  if (!micPermission.value) {
    await setMicPermission();
  }

  if (micPermission.value) {
    isRecording.value = true;
    recognizedText.value = '';
    audioBufferIndex.value = 0;
    sessionId.value = generateRandomString(32);
    if (!recorder) {
      initRecorder();
    }
    recorder.start();

    timerId = setTimeout(async () => {
      showToast('录音已达到最大时长');
      await stopVoiceRecording();
    }, 1000 * 60);
  }
};

// 停止语音录音
const stopVoiceRecording = async () => {
  if (!isRecording.value) return;
  isRecording.value = false;
  if (timerId !== null) {
    clearTimeout(timerId);
    timerId = null;
  }
  if (recorder) {
    recorder.stop();
  }

  releaseMicrophoneResources();

  await getStreamAsr({
    sessionId: sessionId.value,
    format: 'pcm',
    sampleRate: 16000,
    index: audioBufferIndex.value * -1,
    data: null,
  });

  if (recognizedText.value) {
    // 直接发送语音识别的文字
    await handleVoiceSend(recognizedText.value);
  } else {
    showToast('录音解析为空，请重新录制~');
  }
};

// 处理语音发送
const handleVoiceSend = async (content: string) => {
  await handleSendMessage(content);
};

// 通用发送消息方法
const handleSendMessage = async (content: string) => {
  if (!content.trim() || chatStore.answerStatus === AnswerStatusEnum.LOADING) return;

  console.log('🚀 [PersonDetailPopup] 开始发送消息:', content);

  // 如果有正在进行的请求，先取消它
  if (streamController.value) {
    console.log('🔄 [PersonDetailPopup] 取消正在进行的请求');
    streamController.value.abort();
    streamController.value = null;
  }

  // 清空识别文字
  recognizedText.value = '';

  // 重置状态
  isTypewriterStarted.value = false;
  typewriter.done(); // 确保打字机完全停止

  // 添加用户消息
  chatMessages.value.push({
    role: 'user',
    content,
    key: Date.now(),
    isFinish: true,
  });

  // 添加助手消息占位符
  const assistantMessage: IChatStreamContent = {
    role: 'assistant',
    content: '',
    key: Date.now() + 1,
    isFinish: false,
  };
  chatMessages.value.push(assistantMessage);

  // 滚动到底部
  await nextTick(() => {
    scrollChatToBottom();
  });

  // 设置加载状态
  chatStore.setAnswerStatus(AnswerStatusEnum.LOADING);

  try {
    // 创建新的AbortController
    streamController.value = new AbortController();

    // 构建请求数据 - 包含人员信息和用户问题
    const personContext = `当前正在查看${props.personName}的信息。`;
    const finalContent = `${personContext}\n\n用户问题：${content}`;

    const requestData = {
      content: finalContent,
      conversation_id: conversationId.value,
      user_id: props.userId,
    };

    console.log('📤 [PersonDetailPopup] 发送聊天请求:', requestData);

    // 开始流式聊天
    await streamChat(
      requestData,
      {
        onMessage: (messageContent: string) => {
          // 添加数据片段到打字机队列
          typewriter.add(messageContent);

          // 只在第一个数据包到达时启动打字机
          if (!isTypewriterStarted.value) {
            isTypewriterStarted.value = true;
            typewriter.start();
          }
        },
        onPreResponse: (PreResponseContent: string, stage: string) => {
          console.log('🔍 [PersonDetailPopup] 收到预响应内容:', PreResponseContent, stage);
        },
        onToolCall: (toolCall: IToolCall) => {
          console.log('🔧 [PersonDetailPopup] 工具调用:', toolCall);
        },
        onRecommendations: () => {
          // 处理推荐
        },
        onEnd: () => {
          console.log('✅ [PersonDetailPopup] 聊天完成');
          assistantMessage.isFinish = true;
          chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
        },
        onError: (error: Error) => {
          console.error('❌ [PersonDetailPopup] 聊天错误:', error);
          assistantMessage.content = '抱歉，发生了错误，请稍后重试。';
          assistantMessage.isFinish = true;
          chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
        },
      },
      streamController.value.signal,
    );
  } catch (error) {
    console.error('❌ [PersonDetailPopup] 发送消息失败:', error);
    assistantMessage.content = '抱歉，发生了错误，请稍后重试。';
    assistantMessage.isFinish = true;
    chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
  }

  // 滚动到底部
  await nextTick(() => {
    scrollChatToBottom();
  });
};

// 打字机相关
const typewriter = new Typewriter(async (str: string) => {
  if (str && chatMessages.value.length > 0) {
    const lastMessage = chatMessages.value[chatMessages.value.length - 1];
    if (lastMessage.role === 'assistant') {
      lastMessage.content = str;
      await nextTick(() => {
        scrollChatToBottom();
      });
    }
  }
});
const isTypewriterStarted = ref(false);

// 聊天store
const chatStore = useChatStore();

// 判断是否为用户档案
const isUserProfile = computed(() => props.isUserProfile || false);

// 将IPersonDetail转换为IPersonData的函数
const convertPersonDetailToPersonData = (detail: IPersonDetail): IPersonData => {
  // 处理key_attributes，确保它是对象格式并过滤空值
  let keyAttributes: Record<string, string> = {};
  if (detail.key_attributes) {
    if (typeof detail.key_attributes === 'string') {
      try {
        const parsed = JSON.parse(detail.key_attributes) as Record<string, unknown>;
        // 过滤掉空值
        Object.entries(parsed).forEach(([key, value]) => {
          if (value && String(value).trim()) {
            keyAttributes[key] = String(value);
          }
        });
      } catch (error) {
        console.warn('解析key_attributes失败:', error);
        keyAttributes = {};
      }
    } else {
      // 过滤掉空值
      Object.entries(detail.key_attributes).forEach(([key, value]) => {
        if (value && String(value).trim()) {
          keyAttributes[key] = String(value);
        }
      });
    }
  }

  // 处理relationships，确保它是数组格式
  let relationships: string[] = [];
  if (detail.relationships) {
    if (typeof detail.relationships === 'string') {
      try {
        relationships = JSON.parse(detail.relationships);
      } catch (error) {
        console.warn('解析relationships失败:', error);
        relationships = [];
      }
    } else {
      relationships = detail.relationships;
    }
  }

  return {
    person_id: detail.person_id,
    user_id: detail.user_id,
    canonical_name: detail.canonical_name,
    aliases: detail.aliases,
    relationships,
    profile_summary: detail.profile_summary,
    key_attributes: keyAttributes,
    avatar: detail.avatar,
    is_user: detail.is_user,
  };
};

// 处理编辑弹窗关闭
const handleCloseEditDialog = () => {
  showEditDialog.value = false;
  editPersonData.value = null;
};

// 处理编辑成功
const handleEditSuccess = () => {
  console.log('编辑人员信息成功');
  // 重新加载人员详情数据
  void loadPersonDetail();
  // 通知父组件刷新关系图
  emit('refresh');
};

// 处理头像点击
const handleAvatarClick = () => {
  console.log('头像点击，显示头像选择弹窗');
  showAvatarSelectionDialog.value = true;
};

// 处理头像选择
const handleAvatarSelect = async (selectedAvatarId: string) => {
  console.log('✅ [PersonDetailPopup] 选择头像:', selectedAvatarId);

  if (!personDetail.value) return;

  try {
    // 转换数据格式并更新头像
    const personData = convertPersonDetailToPersonData(personDetail.value);

    // 处理aliases字段，确保格式正确
    let submitAliases = personData.aliases || '';
    if (typeof submitAliases !== 'string') {
      submitAliases = '';
    }
    if (submitAliases === '') {
      submitAliases = '[]';
    } else if (!submitAliases.startsWith('[')) {
      // 如果不是JSON格式，按、分割并转换为JSON格式
      const aliasArray = submitAliases
        .split('、')
        .map((alias) => alias.trim())
        .filter((alias) => alias.length > 0)
        .map((alias) => `"${alias}"`);

      if (aliasArray.length > 0) {
        submitAliases = `[${aliasArray.join(',')}]`;
      } else {
        submitAliases = '[]';
      }
    }

    const response = await updatePerson(personDetail.value.person_id, {
      user_id: props.userId,
      canonical_name: personData.canonical_name,
      aliases: submitAliases,
      relationships: personData.relationships as string[],
      profile_summary: personData.profile_summary,
      key_attributes: personData.key_attributes as Record<string, unknown>,
      is_user: personData.is_user,
      avatar: selectedAvatarId,
    });

    if (response && response.result === 'success') {
      console.log('✅ [PersonDetailPopup] 头像更新成功');
      showSuccessToast('头像更新成功');

      // 重新加载人员详情数据
      void loadPersonDetail();
      // 通知父组件刷新关系图
      emit('refresh');

      // 刷新天气数据
      try {
        await refreshWeatherAfterPersonUpdate(props.userId, props.personId);
      } catch (error) {
        console.error('❌ [PersonDetailPopup] 刷新天气数据失败:', error);
      }
    } else {
      console.warn('⚠️ [PersonDetailPopup] 头像更新失败:', response);
      showFailToast('头像更新失败');
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup] 头像更新失败:', error);
    showFailToast('头像更新失败');
  } finally {
    showAvatarSelectionDialog.value = false;
  }
};

// 处理上传头像
const handleUploadAvatar = () => {
  showAvatarSelectionDialog.value = false;
  // 触发隐藏的AvatarUpload组件的上传功能
  if (avatarUploadRef.value && avatarUploadRef.value.triggerUpload) {
    avatarUploadRef.value.triggerUpload();
  }
};

// 处理头像上传成功
const handleAvatarUploadSuccess = async (url: string) => {
  console.log('✅ [PersonDetailPopup] 头像上传成功:', url);
  await handleAvatarSelect(url);
};

// 处理头像上传失败
const handleAvatarUploadError = (error: string) => {
  console.error('❌ [PersonDetailPopup] 头像上传失败:', error);
};

// 处理删除人员按钮点击
const handleDeletePerson = () => {
  showDeleteDialog.value = true;
};

// 关闭删除确认对话框
const closeDeleteDialog = () => {
  showDeleteDialog.value = false;
};

// 确认删除人员
const confirmDeletePerson = async () => {
  if (!personDetail.value) return;

  try {
    isDeleting.value = true;
    console.log('🔄 [PersonDetailPopup.vue] 开始删除人员...', {
      userId: props.userId,
      personId: props.personId,
      name: personDetail.value.canonical_name,
    });

    const response = await deletePerson(props.userId, props.personId);

    console.log('📡 [PersonDetailPopup.vue] 删除人员响应:', response);

    if (response && response.result === 'success') {
      console.log('✅ [PersonDetailPopup.vue] 人员删除成功');
      showSuccessToast('删除成功');

      // 关闭删除对话框
      closeDeleteDialog();

      // 通知父组件刷新关系图
      emit('refresh');

      // 关闭人员详情弹窗
      emit('close');
    } else {
      console.warn('⚠️ [PersonDetailPopup.vue] 删除人员失败:', response);
      showFailToast('删除人员失败');
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup.vue] 删除人员失败:', error);
    showFailToast('删除人员失败');
  } finally {
    isDeleting.value = false;
  }
};

// 处理section-arrow点击事件 - 基础信息
const handleSectionArrowClick = () => {
  console.log('基础信息section-arrow点击');
  if (!isInfoExpanded.value && shouldShowExpansion.value) {
    // 如果文字是折叠状态，先展开
    isInfoExpanded.value = true;
  }
  // 进入编辑模式
  isEditingBasicInfo.value = true;
  basicInfoEditValue.value = personDetail.value?.profile_summary || '';
  // 使用nextTick确保DOM更新后再聚焦
  void nextTick(() => {
    const textarea = document.querySelector('.edit-textarea') as HTMLTextAreaElement;
    if (textarea) {
      textarea.focus();
    }
  });
};

// 处理基础信息编辑完成
const handleBasicInfoEditComplete = async () => {
  if (!personDetail.value) return;

  try {
    const personData = convertPersonDetailToPersonData(personDetail.value);

    // 处理aliases字段
    let submitAliases = personData.aliases || '';
    if (typeof submitAliases !== 'string') {
      submitAliases = '';
    }
    if (submitAliases === '') {
      submitAliases = '[]';
    } else if (!submitAliases.startsWith('[')) {
      const aliasArray = submitAliases
        .split('、')
        .map((alias) => alias.trim())
        .filter((alias) => alias.length > 0)
        .map((alias) => `"${alias}"`);

      if (aliasArray.length > 0) {
        submitAliases = `[${aliasArray.join(',')}]`;
      } else {
        submitAliases = '[]';
      }
    }

    const response = await updatePerson(personDetail.value.person_id, {
      user_id: props.userId,
      canonical_name: personData.canonical_name,
      aliases: submitAliases,
      relationships: personData.relationships as string[],
      profile_summary: basicInfoEditValue.value.trim(),
      key_attributes: personData.key_attributes as Record<string, unknown>,
      is_user: personData.is_user,
      avatar: personData.avatar,
    });

    if (response && response.result === 'success') {
      console.log('✅ [PersonDetailPopup] 基础信息更新成功');
      showSuccessToast('基础信息更新成功');

      // 重新加载人员详情数据
      await loadPersonDetail();
      // 通知父组件刷新关系图
      emit('refresh');

      // 刷新天气数据
      try {
        await refreshWeatherAfterPersonUpdate(props.userId, props.personId);
      } catch (error) {
        console.error('❌ [PersonDetailPopup] 刷新天气数据失败:', error);
      }
    } else {
      console.warn('⚠️ [PersonDetailPopup] 基础信息更新失败:', response);
      showFailToast('基础信息更新失败');
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup] 基础信息更新失败:', error);
    showFailToast('基础信息更新失败');
  } finally {
    isEditingBasicInfo.value = false;
  }
};

// 处理基础信息编辑取消
const handleBasicInfoEditCancel = () => {
  isEditingBasicInfo.value = false;
  basicInfoEditValue.value = '';
};

// 处理天气分析section-arrow点击事件
const handleWeatherSectionArrowClick = () => {
  console.log('天气分析section-arrow点击');
  showWeatherEdit.value = true;
  const attributes = processedKeyAttributes.value;
  weatherEditValue.value = attributes['当前城市'] || '';

  // 使用nextTick确保DOM更新后再聚焦
  void nextTick(() => {
    const input = document.querySelector('.weather-section .attribute-value') as HTMLInputElement;
    if (input) {
      input.focus();
    }
  });
};

// 处理天气分析编辑完成
const handleWeatherEditComplete = async () => {
  if (!personDetail.value) return;

  try {
    const personData = convertPersonDetailToPersonData(personDetail.value);

    // 更新key_attributes中的当前城市
    const updatedKeyAttributes = { ...(personData.key_attributes as Record<string, string>) };
    if (weatherEditValue.value.trim()) {
      updatedKeyAttributes['当前城市'] = weatherEditValue.value.trim();
    } else {
      delete updatedKeyAttributes['当前城市'];
    }

    // 处理aliases字段
    let submitAliases = personData.aliases || '';
    if (typeof submitAliases !== 'string') {
      submitAliases = '';
    }
    if (submitAliases === '') {
      submitAliases = '[]';
    } else if (!submitAliases.startsWith('[')) {
      const aliasArray = submitAliases
        .split('、')
        .map((alias) => alias.trim())
        .filter((alias) => alias.length > 0)
        .map((alias) => `"${alias}"`);

      if (aliasArray.length > 0) {
        submitAliases = `[${aliasArray.join(',')}]`;
      } else {
        submitAliases = '[]';
      }
    }

    const response = await updatePerson(personDetail.value.person_id, {
      user_id: props.userId,
      canonical_name: personData.canonical_name,
      aliases: submitAliases,
      relationships: personData.relationships as string[],
      profile_summary: personData.profile_summary,
      key_attributes: updatedKeyAttributes,
      is_user: personData.is_user,
      avatar: personData.avatar,
    });

    if (response && response.result === 'success') {
      console.log('✅ [PersonDetailPopup] 当前城市更新成功');
      showSuccessToast('当前城市更新成功');

      // 重新加载人员详情数据
      await loadPersonDetail();
      // 通知父组件刷新关系图
      emit('refresh');

      // 刷新天气数据
      try {
        await refreshWeatherAfterPersonUpdate(props.userId, props.personId);
      } catch (error) {
        console.error('❌ [PersonDetailPopup] 刷新天气数据失败:', error);
      }
    } else {
      console.warn('⚠️ [PersonDetailPopup] 当前城市更新失败:', response);
      showFailToast('当前城市更新失败');
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup] 当前城市更新失败:', error);
    showFailToast('当前城市更新失败');
  } finally {
    showWeatherEdit.value = false;
  }
};

// 处理天气分析编辑取消
const handleWeatherEditCancel = () => {
  showWeatherEdit.value = false;
  weatherEditValue.value = '';
};

// 加载推荐话题
const loadRecommendedTopics = async () => {
  try {
    loadingTopics.value = true;
    console.log('🔄 [PersonDetailPopup] 开始获取推荐话题...', {
      userId: props.userId,
      personId: props.personId,
    });

    const response = await getRecommendTopics({
      user_id: props.userId,
      person_id: props.personId,
      max_topics: 2,
      fast_mode: false,
    });

    console.log('📡 [PersonDetailPopup] 推荐话题响应:', response);

    if (response && response.result === 'success' && response.recommended_topics) {
      recommendedTopics.value = response.recommended_topics;
      console.log('✅ [PersonDetailPopup] 推荐话题加载成功:', recommendedTopics.value);
    } else {
      console.warn('⚠️ [PersonDetailPopup] 推荐话题响应格式异常:', response);
      recommendedTopics.value = [];
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup] 获取推荐话题失败:', error);
    showFailToast('获取推荐话题失败');
    recommendedTopics.value = [];
  } finally {
    loadingTopics.value = false;
  }
};

// 处理刷新推荐话题
const handleRefreshTopics = () => {
  console.log('🔄 [PersonDetailPopup] 刷新推荐话题');
  void loadRecommendedTopics();
};

// 处理旅行记录section-arrow点击事件
const handleTravelSectionArrowClick = () => {
  console.log('旅行记录section-arrow点击');
  showTravelEdit.value = true;
  const attributes = processedKeyAttributes.value;
  travelEditValue.value = attributes['旅游历史'] || '';

  // 使用nextTick确保DOM更新后再聚焦
  void nextTick(() => {
    const input = document.querySelector('.travel-section .attribute-value') as HTMLInputElement;
    if (input) {
      input.focus();
    }
  });
};

// 处理旅行记录编辑完成
const handleTravelEditComplete = async () => {
  if (!personDetail.value) return;

  try {
    const personData = convertPersonDetailToPersonData(personDetail.value);

    // 更新key_attributes中的旅游历史
    const updatedKeyAttributes = { ...(personData.key_attributes as Record<string, string>) };
    if (travelEditValue.value.trim()) {
      const currentTravelHistory = updatedKeyAttributes['旅游历史'] || '';
      if (currentTravelHistory) {
        // 如果已有旅游历史，追加新内容
        updatedKeyAttributes['旅游历史'] = `${currentTravelHistory}|${travelEditValue.value.trim()}`;
      } else {
        // 如果没有旅游历史，直接设置
        updatedKeyAttributes['旅游历史'] = travelEditValue.value.trim();
      }
    } else {
      // 如果输入为空，不做任何修改
      // delete updatedKeyAttributes['旅游历史'];
    }

    // 处理aliases字段
    let submitAliases = personData.aliases || '';
    if (typeof submitAliases !== 'string') {
      submitAliases = '';
    }
    if (submitAliases === '') {
      submitAliases = '[]';
    } else if (!submitAliases.startsWith('[')) {
      const aliasArray = submitAliases
        .split('、')
        .map((alias) => alias.trim())
        .filter((alias) => alias.length > 0)
        .map((alias) => `"${alias}"`);

      if (aliasArray.length > 0) {
        submitAliases = `[${aliasArray.join(',')}]`;
      } else {
        submitAliases = '[]';
      }
    }

    const response = await updatePerson(personDetail.value.person_id, {
      user_id: props.userId,
      canonical_name: personData.canonical_name,
      aliases: submitAliases,
      relationships: personData.relationships as string[],
      profile_summary: personData.profile_summary,
      key_attributes: updatedKeyAttributes,
      is_user: personData.is_user,
      avatar: personData.avatar,
    });

    if (response && response.result === 'success') {
      console.log('✅ [PersonDetailPopup] 旅游历史更新成功');
      showSuccessToast('旅游历史更新成功');

      // 重新加载人员详情数据
      await loadPersonDetail();
      // 通知父组件刷新关系图
      emit('refresh');

      // 刷新天气数据
      try {
        await refreshWeatherAfterPersonUpdate(props.userId, props.personId);
      } catch (error) {
        console.error('❌ [PersonDetailPopup] 刷新天气数据失败:', error);
      }
    } else {
      console.warn('⚠️ [PersonDetailPopup] 旅游历史更新失败:', response);
      showFailToast('旅游历史更新失败');
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup] 旅游历史更新失败:', error);
    showFailToast('旅游历史更新失败');
  } finally {
    showTravelEdit.value = false;
  }
};

// 处理旅行记录编辑取消
const handleTravelEditCancel = () => {
  showTravelEdit.value = false;
  travelEditValue.value = '';
};

// 处理饮食偏好section-arrow点击事件
const handleFoodSectionArrowClick = () => {
  console.log('饮食偏好section-arrow点击');
  showFoodEdit.value = true;
  const attributes = processedKeyAttributes.value;
  foodEditValue.value = attributes['餐饮偏好'] || '';

  // 使用nextTick确保DOM更新后再聚焦
  void nextTick(() => {
    const input = document.querySelector('.food-section .attribute-value') as HTMLInputElement;
    if (input) {
      input.focus();
    }
  });
};

// 处理饮食偏好编辑完成
const handleFoodEditComplete = async () => {
  if (!personDetail.value) return;

  try {
    const personData = convertPersonDetailToPersonData(personDetail.value);

    // 更新key_attributes中的餐饮偏好
    const updatedKeyAttributes = { ...(personData.key_attributes as Record<string, string>) };
    if (foodEditValue.value.trim()) {
      const currentFoodPreference = updatedKeyAttributes['餐饮偏好'] || '';
      if (currentFoodPreference) {
        // 如果已有餐饮偏好，追加新内容
        updatedKeyAttributes['餐饮偏好'] = `${currentFoodPreference}|${foodEditValue.value.trim()}`;
      } else {
        // 如果没有餐饮偏好，直接设置
        updatedKeyAttributes['餐饮偏好'] = foodEditValue.value.trim();
      }
    } else {
      // 如果输入为空，不做任何修改
      // delete updatedKeyAttributes['餐饮偏好'];
    }

    // 处理aliases字段
    let submitAliases = personData.aliases || '';
    if (typeof submitAliases !== 'string') {
      submitAliases = '';
    }
    if (submitAliases === '') {
      submitAliases = '[]';
    } else if (!submitAliases.startsWith('[')) {
      const aliasArray = submitAliases
        .split('、')
        .map((alias) => alias.trim())
        .filter((alias) => alias.length > 0)
        .map((alias) => `"${alias}"`);

      if (aliasArray.length > 0) {
        submitAliases = `[${aliasArray.join(',')}]`;
      } else {
        submitAliases = '[]';
      }
    }

    const response = await updatePerson(personDetail.value.person_id, {
      user_id: props.userId,
      canonical_name: personData.canonical_name,
      aliases: submitAliases,
      relationships: personData.relationships as string[],
      profile_summary: personData.profile_summary,
      key_attributes: updatedKeyAttributes,
      is_user: personData.is_user,
      avatar: personData.avatar,
    });

    if (response && response.result === 'success') {
      console.log('✅ [PersonDetailPopup] 餐饮偏好更新成功');
      showSuccessToast('餐饮偏好更新成功');

      // 重新加载人员详情数据
      await loadPersonDetail();
      // 通知父组件刷新关系图
      emit('refresh');

      // 刷新天气数据
      try {
        await refreshWeatherAfterPersonUpdate(props.userId, props.personId);
      } catch (error) {
        console.error('❌ [PersonDetailPopup] 刷新天气数据失败:', error);
      }
    } else {
      console.warn('⚠️ [PersonDetailPopup] 餐饮偏好更新失败:', response);
      showFailToast('餐饮偏好更新失败');
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup] 餐饮偏好更新失败:', error);
    showFailToast('餐饮偏好更新失败');
  } finally {
    showFoodEdit.value = false;
  }
};

// 处理饮食偏好编辑取消
const handleFoodEditCancel = () => {
  showFoodEdit.value = false;
  foodEditValue.value = '';
};

// 处理我的期望section-arrow点击事件
const handleExpectationSectionArrowClick = () => {
  console.log('我的期望section-arrow点击');
  showExpectationEdit.value = true;
  const attributes = processedKeyAttributes.value;
  expectationEditValue.value = attributes['期望'] || '';

  // 使用nextTick确保DOM更新后再聚焦
  void nextTick(() => {
    const input = document.querySelector('.expectation-section .attribute-value') as HTMLInputElement;
    if (input) {
      input.focus();
    }
  });
};

// 处理我的期望编辑完成
const handleExpectationEditComplete = async () => {
  if (!personDetail.value) return;

  try {
    const personData = convertPersonDetailToPersonData(personDetail.value);

    // 更新key_attributes中的期望
    const updatedKeyAttributes = { ...(personData.key_attributes as Record<string, string>) };
    if (expectationEditValue.value.trim()) {
      updatedKeyAttributes['期望'] = expectationEditValue.value.trim();
    } else {
      delete updatedKeyAttributes['期望'];
    }

    // 处理aliases字段
    let submitAliases = personData.aliases || '';
    if (typeof submitAliases !== 'string') {
      submitAliases = '';
    }
    if (submitAliases === '') {
      submitAliases = '[]';
    } else if (!submitAliases.startsWith('[')) {
      const aliasArray = submitAliases
        .split('、')
        .map((alias) => alias.trim())
        .filter((alias) => alias.length > 0)
        .map((alias) => `"${alias}"`);

      if (aliasArray.length > 0) {
        submitAliases = `[${aliasArray.join(',')}]`;
      } else {
        submitAliases = '[]';
      }
    }

    const response = await updatePerson(personDetail.value.person_id, {
      user_id: props.userId,
      canonical_name: personData.canonical_name,
      aliases: submitAliases,
      relationships: personData.relationships as string[],
      profile_summary: personData.profile_summary,
      key_attributes: updatedKeyAttributes,
      is_user: personData.is_user,
      avatar: personData.avatar,
    });

    if (response && response.result === 'success') {
      console.log('✅ [PersonDetailPopup] 期望更新成功');
      showSuccessToast('期望更新成功');

      // 重新加载人员详情数据
      await loadPersonDetail();
      // 通知父组件刷新关系图
      emit('refresh');

      // 刷新天气数据
      try {
        await refreshWeatherAfterPersonUpdate(props.userId, props.personId);
      } catch (error) {
        console.error('❌ [PersonDetailPopup] 刷新天气数据失败:', error);
      }
    } else {
      console.warn('⚠️ [PersonDetailPopup] 期望更新失败:', response);
      showFailToast('期望更新失败');
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup] 期望更新失败:', error);
    showFailToast('期望更新失败');
  } finally {
    showExpectationEdit.value = false;
  }
};

// 处理我的期望编辑取消
const handleExpectationEditCancel = () => {
  showExpectationEdit.value = false;
  expectationEditValue.value = '';
};

// 语音聊天弹窗相关状态
const showVoiceChatDialog = ref(false);
const voiceChatSectionInfo = ref<{
  title: string;
  icon: string;
  content: string;
}>({
  title: '',
  icon: '',
  content: '',
});

// 处理各个section的mic按钮点击事件
const handleBasicInfoMicClick = () => {
  console.log('基础信息mic按钮点击');
  voiceChatSectionInfo.value = {
    title: '基础信息',
    icon: '📋',
    content: personDetail.value?.profile_summary || '暂无基础信息',
  };
  showVoiceChatDialog.value = true;
};

const handleWeatherMicClick = () => {
  console.log('天气分析mic按钮点击');
  const attributes = processedKeyAttributes.value;
  voiceChatSectionInfo.value = {
    title: '天气分析',
    icon: '☀️',
    content: attributes['当前城市'] || '暂无天气信息',
  };
  showVoiceChatDialog.value = true;
};

const handleTravelMicClick = () => {
  console.log('旅行记录mic按钮点击');
  const attributes = processedKeyAttributes.value;
  voiceChatSectionInfo.value = {
    title: '旅行记录',
    icon: '✈️',
    content: attributes['旅游历史'] || '暂无旅行记录',
  };
  showVoiceChatDialog.value = true;
};

const handleFoodMicClick = () => {
  console.log('饮食偏好mic按钮点击');
  const attributes = processedKeyAttributes.value;
  voiceChatSectionInfo.value = {
    title: '饮食偏好',
    icon: '🍽️',
    content: attributes['餐饮偏好'] || '暂无饮食偏好信息',
  };
  showVoiceChatDialog.value = true;
};

const handleExpectationMicClick = () => {
  console.log('我的期望mic按钮点击');
  const attributes = processedKeyAttributes.value;
  voiceChatSectionInfo.value = {
    title: '我的期望',
    icon: '🌟',
    content: attributes['期望'] || '暂无期望信息',
  };
  showVoiceChatDialog.value = true;
};

// 处理语音聊天弹窗关闭
const handleVoiceChatDialogClose = () => {
  showVoiceChatDialog.value = false;
};

// 更新VoiceChatDialog中显示的section信息
const updateVoiceChatSectionInfo = () => {
  const currentTitle = voiceChatSectionInfo.value.title;

  if (currentTitle === '基础信息') {
    voiceChatSectionInfo.value = {
      title: '基础信息',
      icon: '📋',
      content: personDetail.value?.profile_summary || '暂无基础信息',
    };
  } else if (currentTitle === '天气分析') {
    const attributes = processedKeyAttributes.value;
    voiceChatSectionInfo.value = {
      title: '天气分析',
      icon: '☀️',
      content: attributes['当前城市'] || '暂无天气信息',
    };
  } else if (currentTitle === '旅行记录') {
    const attributes = processedKeyAttributes.value;
    voiceChatSectionInfo.value = {
      title: '旅行记录',
      icon: '✈️',
      content: attributes['旅游历史'] || '暂无旅行记录',
    };
  } else if (currentTitle === '饮食偏好') {
    const attributes = processedKeyAttributes.value;
    voiceChatSectionInfo.value = {
      title: '饮食偏好',
      icon: '🍽️',
      content: attributes['餐饮偏好'] || '暂无饮食偏好信息',
    };
  } else if (currentTitle === '我的期望') {
    const attributes = processedKeyAttributes.value;
    voiceChatSectionInfo.value = {
      title: '我的期望',
      icon: '🌟',
      content: attributes['期望'] || '暂无期望信息',
    };
  }
};

// 处理语音聊天完成事件
const handleVoiceChatComplete = async () => {
  // 调用数据刷新函数
  await refreshDataAfterChat();

  // 数据刷新完成后，更新VoiceChatDialog中显示的section信息
  updateVoiceChatSectionInfo();
};

// 鼠标按下时的位置记录
const mouseDownPosition = { x: 0, y: 0 };

// 处理鼠标按下事件
const handleMouseDown = (event: MouseEvent) => {
  mouseDownPosition.x = event.clientX;
  mouseDownPosition.y = event.clientY;
};

// 处理鼠标松开事件
const handleMouseUp = (event: MouseEvent) => {
  // 计算鼠标移动距离
  const deltaX = Math.abs(event.clientX - mouseDownPosition.x);
  const deltaY = Math.abs(event.clientY - mouseDownPosition.y);

  // 如果鼠标移动距离很小（小于5像素），并且没有选中文本，则认为是点击操作
  const isClick = deltaX < 5 && deltaY < 5;
  const selection = window.getSelection();
  const hasSelection = selection ? selection.toString().length > 0 : false;

  if (isClick && !hasSelection && shouldShowExpansion.value) {
    isInfoExpanded.value = !isInfoExpanded.value;
  }
};

// 检查文本是否需要展开功能
const checkTextExpansion = (text: string) => {
  if (!text) {
    shouldShowExpansion.value = false;
    return;
  }

  // 简单的行数估算：假设每行约40个字符
  const estimatedLines = Math.ceil(text.length / 20);
  shouldShowExpansion.value = estimatedLines > 3;

  if (!shouldShowExpansion.value) {
    isInfoExpanded.value = true; // 如果不需要展开功能，默认显示全部
  }
};

// Tab切换方法 - 整体切换
const toggleTab = () => {
  const newTab = activeTab.value === 'info' ? 'chat' : 'info';

  // 如果从聊天tab切换到其他tab，清理正在进行的SSE连接
  if (activeTab.value === 'chat' && newTab !== 'chat') {
    resetChatState();
  }

  activeTab.value = newTab;
  if (newTab === 'chat') {
    // 切换到聊天tab时，初始化会话并更新mention宽度
    void initChatConversation();
    // 更新mention选择器宽度
    setTimeout(async () => {
      await updateMentionWidth();
    }, 10);
  }
};

// 初始化聊天会话
const initChatConversation = async () => {
  // 确保聊天状态是干净的
  resetChatState();

  if (!conversationId.value) {
    try {
      const response = await createConversation({
        user_id: props.userId,
      });
      if (response && response.conversation_id) {
        conversationId.value = response.conversation_id;
        console.log('✅ [PersonDetailPopup] 聊天会话初始化成功:', conversationId.value);
      }
    } catch (error) {
      console.error('❌ [PersonDetailPopup] 初始化聊天会话失败:', error);
    }
  }
};

// 滚动聊天消息到底部
const scrollChatToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight;
  }
};

// 重置聊天状态
const resetChatState = () => {
  console.log('🔄 [PersonDetailPopup] 重置聊天状态');

  // 取消正在进行的请求
  if (streamController.value) {
    streamController.value.abort();
    streamController.value = null;
  }

  // 停止打字机
  typewriter.done();

  // 重置状态标志
  isTypewriterStarted.value = false;

  // 重置聊天状态
  chatStore.setAnswerStatus(AnswerStatusEnum.SUCCESS);
};

// 发送聊天消息
const handleSendChatMessage = async () => {
  const content = chatInput.value.trim();
  if (!content || chatStore.answerStatus === AnswerStatusEnum.LOADING) return;

  chatInput.value = ''; // 清空输入框
  await handleSendMessage(content);
};

// 对话结束后刷新数据
const refreshDataAfterChat = async () => {
  console.log('🔄 [PersonDetailPopup] 对话结束，开始刷新人员信息和提醒数据...');

  try {
    // 并行调用API刷新数据
    await Promise.all([refreshPersonDetail(), refreshReminders()]);
    console.log('✅ [PersonDetailPopup] 人员信息和提醒数据刷新完成');

    // 显示成功提示
    showSuccessToast('信息已更新');
  } catch (error) {
    console.error('❌ [PersonDetailPopup] 刷新数据失败:', error);
    showFailToast('刷新数据失败');
  }
};

// 刷新人员详情数据
const refreshPersonDetail = async () => {
  await loadPersonDetail();
};

// 刷新提醒数据
const refreshReminders = async () => {
  await loadReminders();
};

// 获取人员详情数据
const loadPersonDetail = async () => {
  try {
    loading.value = true;
    console.log('🔄 [PersonDetailPopup.vue] 开始获取人员详情...', {
      userId: props.userId,
      personId: props.personId,
      personName: props.personName,
      isUserProfile: isUserProfile.value,
    });

    if (!props.userId || !props.personId) {
      console.error('❌ [PersonDetailPopup.vue] 缺少必要参数');
      return;
    }

    let response: IGetUserProfileResponse | IGetPersonDetailResponse;

    // 判断是否为用户档案
    if (isUserProfile.value) {
      // 获取用户档案
      console.log('🔄 [PersonDetailPopup.vue] 获取用户档案...');
      response = await getUserProfile({
        user_id: props.userId,
      });
    } else {
      // 获取普通人员详情
      response = await getPersonDetail({
        user_id: props.userId,
        person_id: props.personId,
      });
    }

    console.log('📡 [PersonDetailPopup.vue] 人员详情响应:', response);

    if (response && response.result === 'success' && response.person) {
      // 类型转换：将IPersonData转换为IPersonDetail兼容格式
      personDetail.value = response.person as IPersonDetail;
      console.log('✅ [PersonDetailPopup.vue] 人员详情加载成功');

      // 检查基础信息文本是否需要展开功能
      checkTextExpansion(response.person.profile_summary || '');
    } else {
      console.warn('⚠️ [PersonDetailPopup.vue] 人员详情数据格式异常');
      if (isUserProfile.value && 'reason' in response) {
        showFailToast(typeof response.reason === 'string' ? response.reason : '获取用户档案失败');
      }
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup.vue] 获取人员详情失败:', error);
    showFailToast('获取人员详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取天气数据
const loadWeatherData = async () => {
  try {
    loadingWeather.value = true;

    if (!props.userId || !props.personId) {
      console.error('❌ [PersonDetailPopup.vue] 缺少必要参数');
      weatherData.value = null;
      return;
    }

    console.log('🔄 [PersonDetailPopup.vue] 开始获取天气数据...', {
      userId: props.userId,
      personId: props.personId,
      isUserProfile: isUserProfile.value,
    });

    const response = await getPersonWeather({
      user_id: props.userId,
      person_id: props.personId,
    });

    console.log('📡 [PersonDetailPopup.vue] 天气数据响应:', response);

    if (response && response.result === 'success') {
      weatherData.value = response;
      console.log('✅ [PersonDetailPopup.vue] 天气数据加载成功');
    } else if (response && response.result === 'error') {
      weatherData.value = response;
      console.log('⚠️ [PersonDetailPopup.vue] 天气数据返回错误:', response.reason);
    } else {
      console.warn('⚠️ [PersonDetailPopup.vue] 天气数据格式异常:', response);
      weatherData.value = null;
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup.vue] 获取天气数据失败:', error);
    // 区分不同类型的错误
    if (error instanceof Error) {
      if (error.message.includes('timeout') || error.message.includes('Fetch timeout')) {
        console.error('❌ [PersonDetailPopup.vue] 天气API请求超时');
      } else if (error.message.includes('network') || error.message.includes('Failed to fetch')) {
        console.error('❌ [PersonDetailPopup.vue] 天气API网络错误');
      } else {
        console.error('❌ [PersonDetailPopup.vue] 天气API其他错误:', error.message);
      }
    }
    weatherData.value = null;
  } finally {
    loadingWeather.value = false;
  }
};

// 获取提醒列表
const loadReminders = async () => {
  try {
    loadingReminders.value = true;
    console.log('🔄 [PersonDetailPopup.vue] 开始获取提醒列表...', {
      userId: props.userId,
    });

    if (!props.userId) {
      console.error('❌ [PersonDetailPopup.vue] 缺少用户ID');
      return;
    }

    const response = await getReminders({
      user_id: props.userId,
    });

    console.log('📡 [PersonDetailPopup.vue] 提醒列表响应:', response);

    if (response && response.success) {
      reminders.value = response.reminders || [];
      console.log('✅ [PersonDetailPopup.vue] 提醒列表加载成功，共', reminders.value.length, '条');
    } else {
      console.warn('⚠️ [PersonDetailPopup.vue] 提醒列表数据格式异常');
      reminders.value = [];
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup.vue] 获取提醒列表失败:', error);
    showFailToast('获取提醒列表失败');
    reminders.value = [];
  } finally {
    loadingReminders.value = false;
  }
};

// 处理添加提醒
const handleAddReminder = () => {
  reminderToEdit.value = null; // 清空编辑状态
  showAddReminderDialog.value = true;
};

// 处理编辑提醒
const handleEditReminder = (reminder: IReminder) => {
  reminderToEdit.value = reminder;
  showEditReminderDialog.value = true;
};

// 处理删除提醒
const handleDeleteReminder = (reminder: IReminder) => {
  reminderToDelete.value = reminder;
  showDeleteReminderDialog.value = true;
};

// 确认删除提醒
const confirmDeleteReminder = async () => {
  if (!reminderToDelete.value) return;

  try {
    console.log('🔄 [PersonDetailPopup.vue] 开始删除提醒...', {
      userId: props.userId,
      reminderId: reminderToDelete.value.reminder_id,
    });

    const response = await deleteReminder({
      user_id: props.userId,
      reminder_id: reminderToDelete.value.reminder_id,
    });

    console.log('📡 [PersonDetailPopup.vue] 删除提醒响应:', response);

    if (response && response.success) {
      console.log('✅ [PersonDetailPopup.vue] 提醒删除成功');
      showSuccessToast('提醒删除成功');

      // 从列表中移除已删除的提醒
      reminders.value = reminders.value.filter((r) => r.reminder_id !== reminderToDelete.value!.reminder_id);

      // 关闭删除对话框
      closeDeleteReminderDialog();
    } else {
      console.warn('⚠️ [PersonDetailPopup.vue] 删除提醒失败:', response);
      showFailToast(response?.message || '删除提醒失败');
    }
  } catch (error) {
    console.error('❌ [PersonDetailPopup.vue] 删除提醒失败:', error);
    showFailToast('删除提醒失败');
  }
};

// 关闭删除提醒对话框
const closeDeleteReminderDialog = () => {
  showDeleteReminderDialog.value = false;
  reminderToDelete.value = null;
};

// 处理添加提醒成功
const handleAddReminderSuccess = () => {
  showAddReminderDialog.value = false;
  // 重新加载提醒列表
  void loadReminders();
};

// 处理编辑提醒成功
const handleEditReminderSuccess = () => {
  showEditReminderDialog.value = false;
  reminderToEdit.value = null;
  // 重新加载提醒列表
  void loadReminders();
};

// 监听enableMention变化，更新mention宽度
watch(
  enableMention,
  (newValue) => {
    if (newValue) {
      // 使用setTimeout确保DOM已经更新
      setTimeout(async () => {
        await updateMentionWidth();
      }, 10);
    }
  },
  { immediate: false },
);

// 监听personName变化，重新计算mention宽度
watch(
  () => props.personName,
  () => {
    if (enableMention.value) {
      // 使用setTimeout确保DOM已经更新
      setTimeout(async () => {
        await updateMentionWidth();
      }, 10);
    }
  },
  { immediate: false },
);

// 天气数据刷新订阅
let weatherRefreshUnsubscribe: (() => void) | null = null;

// 处理天气数据刷新事件
const handleWeatherRefresh = (event: IWeatherRefreshEvent) => {
  if (event.type === 'person-weather' && event.personId === props.personId) {
    console.log('🔄 [PersonDetailPopup] 收到人员天气数据刷新事件，更新weather-reminder');
    // 更新天气数据
    weatherData.value = event.data as IGetPersonWeatherResponse;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  void loadPersonDetail();
  void loadWeatherData();
  void loadReminders();
  void loadRecommendedTopics();

  // 订阅天气数据刷新事件
  weatherRefreshUnsubscribe = weatherRefreshManager.subscribe(handleWeatherRefresh);
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
  console.log('🧹 [PersonDetailPopup] 组件卸载，清理资源');
  resetChatState();

  // 清理语音录音资源
  if (isRecording.value) {
    if (recorder) {
      recorder.stop();
    }
    isRecording.value = false;
  }
  releaseMicrophoneResources();

  // 取消天气数据刷新订阅
  if (weatherRefreshUnsubscribe) {
    weatherRefreshUnsubscribe();
    weatherRefreshUnsubscribe = null;
  }
});
</script>

<style lang="scss" scoped>
.person-detail-popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.person-detail-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  padding-bottom: 0; // 底部不需要padding，为chat-input-container留空间
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 600px;
  height: 900px; // 固定高度
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 防止整个容器滚动
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.person-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  position: relative;
}

.person-name-section {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .person-name {
    font-size: 28px; // 增加4px (原来24px)
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    text-align: left;
  }
}

.header-controls-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center; // 改为居中对齐，保持一致高度
  position: relative;
  height: 72px; // 调整头部高度以适应新的布局
}

.avatar-section {
  display: flex;
  align-items: center;
  height: 60px; // 调整高度以适应缩小的头像

  .person-avatar {
    width: 60px; // 缩小头像尺寸
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(139, 69, 19, 0.8); // 棕色边框
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

    &.clickable-avatar {
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        border-color: #00ffff;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
        transform: scale(1.05);
      }
    }
  }
}

.header-action-buttons {
  display: flex;
  gap: 20px;
  align-items: center;
  flex: 1;
  justify-content: center; // 改为居中对齐
  height: 72px;

  .tab-switch {
    position: relative;
    display: flex;
    background: rgba(0, 188, 212, 0.1);
    border: 2px solid #00bcd4;
    border-radius: 28px; // 增大圆角
    padding: 6px; // 增大内边距
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    width: 310px; // 适度增大宽度以适应更大字体
    min-width: 310px; // 确保最小宽度
    height: 56px; // 增大高度

    &:hover {
      background: rgba(0, 188, 212, 0.15);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }

    .tab-slider {
      position: absolute;
      top: 6px;
      left: 6px;
      width: calc(50% - 6px);
      height: calc(100% - 12px);
      background: #00bcd4;
      border-radius: 22px;
      transition: transform 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 188, 212, 0.4);

      &.slide-right {
        transform: translateX(100%);
      }
    }

    .tab-option {
      flex: 1;
      padding: 12px 22px; // 增大内边距
      text-align: center;
      font-size: 25px; // 增加8px (原来22px)
      font-weight: 500;
      color: #00bcd4;
      transition: color 0.3s ease;
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;

      &.active {
        color: white;
      }
    }
  }
}

.close-section {
  display: flex;
  align-items: center;
  height: 72px; // 调整高度以适应新的布局

  .close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.content-sections {
  display: flex;
  flex-direction: column;
  flex: 1; // 占据剩余空间
  overflow-y: auto; // 允许内容区域滚动
  padding-right: 4px; // 为滚动条留出空间
  min-height: 0; // 确保flex子项可以收缩

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.info-section,
.attribute-section,
.reminder-section,
.weather-section,
.topic-section,
.travel-section,
.food-section,
.expectation-section,
.info-display-section {
  border: none;
  border-radius: 16px;
  padding: 22px; // 增加2px上下padding (原来20px)
  margin-top: 24px; // 增加4px section间距 (原来20px)
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  max-height: 260px;
  display: flex;
  flex-direction: column;
}

// 提醒相关样式
.reminder-section .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 信息显示区域样式
.info-display-section {
  .info-display-content {
    .info-item {
      display: flex;
      margin-bottom: 8px;
      font-size: 24px; // 增加8px (原来16px)

      .info-label {
        color: #00ffff;
        font-weight: 500;
        min-width: 80px;
        margin-right: 8px;
        font-size: 30px; // 增加8px (原来22px)
      }

      .info-value {
        color: white;
        flex: 1;
        word-break: break-word;
        font-size: 30px; // 增加8px (原来22px)
      }
    }

    .attributes-display {
      margin-top: 12px;
    }
  }
}

.add-reminder-btn {
  background: transparent;
  color: #00bcd4;
  border: 2px solid #00bcd4;
  border-radius: 20px;
  padding: 12px 20px;
  font-size: 32px; // 增加8px (原来24px)
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 48px;

  &:hover {
    background: rgba(0, 188, 212, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
  }

  .add-icon {
    font-size: 32px; // 增加8px (原来24px)
    font-weight: bold;
  }
}

.reminder-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.reminder-item {
  background: rgba(0, 188, 212, 0.05);
  border: 2px solid rgba(0, 188, 212, 0.3);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(0, 188, 212, 0.5);
    background: rgba(0, 188, 212, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.2);
  }
}

.reminder-info {
  flex: 1;

  .reminder-text {
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px; // 增加8px (原来24px)
    font-weight: 600;
    margin-bottom: 12px;
  }
}

.reminder-actions {
  display: flex;
  gap: 12px;
  margin-left: 20px;
}

.edit-reminder-btn,
.delete-reminder-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  color: white;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 28px; // 增加8px (原来20px)
  font-weight: 600;
  height: 48px;
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;

  .edit-icon,
  .delete-icon {
    font-size: 28px; // 增加8px (原来20px)
  }
}

.edit-reminder-btn {
  &:hover {
    background: rgba(0, 188, 212, 0.2);
    border-color: #00bcd4;
    color: #00bcd4;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
  }
}

.delete-reminder-btn {
  &:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: #ef4444;
    color: #ef4444;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;

  .section-icon {
    font-size: 32px; // 增加8px (原来24px)
  }

  .section-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 34px; // 增加8px (原来26px)
    font-weight: 600;
    flex: 1;
  }

  .section-actions {
    display: flex;
    align-items: center;
    gap: 14px; // 增加6px间距防止误触 (原来8px)
  }

  .section-edit-btn,
  .section-mic-btn {
    width: 42px; // 适当增大以适应页面文字变大 (原来36px)
    height: 42px;
    border-radius: 50%;
    border: 2px solid #00bcd4;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }

    .section-edit-icon,
    .section-mic-icon {
      width: 18px;
      height: 18px;
      filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%)
        contrast(96%);
    }
  }

  .refresh-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: 2px solid #00bcd4;
    background: transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;

    &:hover:not(:disabled) {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .refresh-icon {
      width: 18px;
      height: 18px;
      filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%)
        contrast(96%);
      transition: transform 0.3s ease;
    }

    &:hover:not(:disabled) .refresh-icon {
      transform: rotate(180deg);
    }
  }
}

.section-content {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.loading-text {
  color: rgba(255, 255, 255, 0.6);
  font-style: italic;
  text-align: center;
  padding: 10px 0;
  font-size: 32px; // 增加8px (原来24px)
}

.info-content {
  .info-text-container {
    transition: all 0.3s ease;

    &.expanded .info-text.collapsed {
      -webkit-line-clamp: unset;
      line-clamp: unset;
      max-height: none;
    }

    .info-text {
      margin: 0 0 16px 0;
      font-size: 32px; // 增加8px (原来24px)
      line-height: 1.6;
      transition: all 0.3s ease;
      user-select: text; // 允许文本选择
      cursor: text; // 文本光标

      &.collapsed {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.attribute-content {
  .attribute-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    &:last-child {
      margin-bottom: 0;
      border-bottom: none;
    }

    .attribute-value {
      color: rgba(255, 255, 255, 0.9);
      font-size: 30px; // 增加8px (原来22px)
      line-height: 1.5;
      flex: 1;
    }
  }

  .no-attributes {
    color: rgba(255, 255, 255, 0.6);
    font-size: 30px; // 增加8px (原来22px)
    font-style: italic;
    text-align: center;
    padding: 20px 0;
  }
}

.topic-content {
  .topic-item {
    margin-bottom: 8px;
    font-size: 32px; // 增加8px (原来24px)
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .empty-topic {
    color: rgba(255, 255, 255, 0.6);
    font-style: italic;
    text-align: center;
    padding: 10px 0;
    font-size: 32px; // 增加8px (原来24px)
  }
}

.travel-content,
.food-content,
.expectation-content {
  .travel-info,
  .food-info,
  .expectation-info {
    color: #ffffff;
    font-size: 32px; // 增加8px (原来24px)
    line-height: 1.6;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
  }

  .empty-travel,
  .empty-food,
  .empty-expectation {
    color: rgba(255, 255, 255, 0.6);
    font-size: 30px; // 增加8px (原来22px)
    font-style: italic;
    text-align: center;
    padding: 20px 0;
  }
}

.weather-content {
  .weather-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .weather-location {
      font-size: 26px; // 增加8px (原来18px)
      font-weight: 600;
      color: #00bcd4;
      margin-bottom: 8px;
    }

    .weather-info {
      background: rgba(0, 188, 212, 0.1);
      border: 1px solid rgba(0, 188, 212, 0.2);
      border-radius: 12px;
      padding: 16px;
      font-size: 26px; // 增加8px (原来18px)
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.9);
    }
  }

  .weather-error {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid rgba(255, 107, 107, 0.2);
    border-radius: 12px;
    padding: 16px;

    .error-title {
      font-size: 26px; // 增加8px (原来18px)
      font-weight: 600;
      line-height: 1.6;
      color: rgba(255, 107, 107, 1);
      margin-bottom: 12px;
    }

    .error-message {
      font-size: 24px; // 增加8px (原来16px)
      line-height: 1.6;
      color: rgba(255, 107, 107, 0.9);
      margin-bottom: 12px;
    }

    .error-suggestion {
      font-size: 24px; // 增加8px (原来16px)
      line-height: 1.5;
      color: rgba(255, 255, 255, 0.8);
      background: rgba(255, 255, 255, 0.05);
      padding: 12px;
      border-radius: 8px;
      border-left: 3px solid rgba(255, 193, 7, 0.8);
    }
  }

  .weather-suggestion {
    background: rgba(0, 188, 212, 0.1);
    border: 1px solid rgba(0, 188, 212, 0.2);
    border-radius: 12px;
    padding: 16px;
    font-size: 28px; // 增加8px (原来20px)
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
  }

  .weather-reminder {
    background: rgba(0, 188, 212, 0.1);
    border: 1px solid rgba(0, 188, 212, 0.2);
    border-radius: 12px;
    padding: 16px;
    font-size: 28px; // 增加8px (原来20px)
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
  }
}

// 新增部分的样式
.reminder-content,
.travel-content,
.food-content,
.expectation-content {
  .empty-reminder,
  .empty-travel,
  .empty-food,
  .empty-expectation {
    color: rgba(255, 255, 255, 0.6);
    font-size: 30px; // 增加8px (原来22px)
    font-style: italic;
    text-align: center;
    padding: 20px 0;
    background: rgba(0, 188, 212, 0.05);
    border: 1px dashed rgba(0, 188, 212, 0.3);
    border-radius: 12px;
    line-height: 1.6;
  }
}

// 聊天界面样式
.chat-tab-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 0; // 移除间隙，让聊天消息和输入框紧密连接
  min-height: 0; // 确保flex子项可以收缩
}

.chat-prompt {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 32px; // 增加8px (原来24px)
  padding: 20px;
  background: rgba(0, 188, 212, 0.05);
  border-radius: 16px;
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
}

// 聊天消息列表
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 16px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

// 聊天消息
.chat-message {
  display: flex;
  margin-bottom: 16px;

  &.user {
    justify-content: flex-end;

    .message-content {
      background: rgba(0, 188, 212, 0.2);
      border: 1px solid rgba(0, 188, 212, 0.3);
      border-radius: 18px 18px 4px 18px;
      padding: 12px 16px;
      max-width: 70%;
      color: rgba(255, 255, 255, 0.9);
      font-size: 30px; // 增加8px (原来22px)
      line-height: 1.5;
      word-wrap: break-word;
    }
  }

  &.assistant {
    justify-content: flex-start;

    .message-content {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 18px 18px 18px 4px;
      padding: 12px 16px;
      max-width: 70%;
      color: rgba(255, 255, 255, 0.8);
      font-size: 30px; // 增加8px (原来22px)
      line-height: 1.5;
      word-wrap: break-word;

      &.loading-content {
        min-height: 40px;
        display: flex;
        align-items: center;
      }
    }
  }
}

// Loading动画
.loading {
  display: flex;
  gap: 4px;
  align-items: center;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    animation: loadingDot 1.4s infinite ease-in-out both;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }

    &:nth-child(3) {
      animation-delay: 0s;
    }
  }
}

@keyframes loadingDot {
  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 新的底部操作区域样式 - 位于内容区域内部
.person-footer-in-content {
  margin-top: 40px; // 与上方内容保持距离
  padding: 30px 0; // 上下内边距
  border-top: 1px solid rgba(255, 255, 255, 0.1); // 更淡的分割线

  .delete-person-btn {
    width: 50%;
    margin: 0 auto;
    background: transparent;
    color: #ef4444;
    border: 2px solid #ef4444;
    border-radius: 20px;
    padding: 16px 16px;
    font-size: 36px; // 增加8px (原来28px)
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    height: 60px;

    &:hover {
      background: rgba(239, 68, 68, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }

    .delete-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(346deg) brightness(104%)
        contrast(97%);
    }

    .delete-text {
      font-size: 36px; // 增加8px (原来28px)
      font-weight: 600;
    }
  }
}

.chat-input-container {
  padding: 20px 0px 30px 0px; // 移除左右内边距，让内容占满宽度
  background: transparent; // 改为透明背景
  border-radius: 0 0 16px 16px; // 只有底部圆角，与弹窗底部贴合
  border: none; // 移除边框
  box-shadow: none; // 移除阴影
  margin-top: auto; // 推到底部
  flex-shrink: 0; // 防止被压缩
}

.chat-input-wrapper {
  display: flex;
  gap: 16px; // 增大间距
  align-items: center;
  position: relative;
  width: 100%; // 占满容器宽度
  padding: 0 0px; // 在wrapper内部添加左右内边距
  box-sizing: border-box;

  .mention-selector {
    position: absolute;
    left: 22px; // 与输入框的padding-left保持一致
    top: 50%;
    transform: translateY(-50%); // 垂直居中
    z-index: 10;

    .mention-checkbox {
      display: flex;
      align-items: center;
      gap: 6px; // 增加间距
      cursor: pointer;
      user-select: none;
      background: rgba(0, 188, 212, 0.2);
      padding: 6px 10px; // 增加内边距
      border-radius: 14px; // 增大圆角
      border: 1px solid #00bcd4;

      .mention-label {
        color: #00bcd4;
        font-size: 26px; // 增加8px (原来18px)
        font-weight: 500;
        transition: color 0.3s ease;
      }

      &:hover {
        .mention-label {
          color: #00e5ff;
        }
      }
    }
  }

  .chat-input {
    flex: 1;
    padding: 18px 22px; // 进一步增大内边距
    border: 2px solid rgba(0, 188, 212, 0.3);
    border-radius: 30px; // 进一步增大圆角
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 30px; // 增加8px (原来22px)
    outline: none;
    transition: all 0.3s ease;
    min-height: 56px; // 增加最小高度
    box-sizing: border-box;

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }

    &:focus {
      border-color: #00bcd4;
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 0 0 3px rgba(0, 188, 212, 0.2);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .send-btn {
    width: 56px; // 进一步增大按钮尺寸
    height: 56px;
    border-radius: 50%;
    border: 2px solid #00bcd4;
    background: #00bcd4;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover:not(:disabled) {
      background: #00acc1;
      border-color: #00acc1;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.4);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    svg {
      width: 26px; // 进一步增大图标尺寸
      height: 26px;
    }
  }
}

// 语音对话区域
.voice-chat-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: none;
  border-radius: 16px;
  padding: 20px;
  background: rgba(0, 188, 212, 0.05);
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  overflow: hidden;
}

// 语音输入区域样式
.voice-input-area {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

// 识别文字显示区域
.recognized-text {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 24px; // 增加8px (原来16px)
  text-align: center;
  max-width: 80%;
  word-wrap: break-word;
  margin-bottom: 8px;
}

// 语音输入模式
.voice-chat-dialog .voice-input-mode {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  gap: 20px !important;
  position: relative !important;

  // 语音提示文字
  .voice-prompt-text {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 28px !important; // 增加8px (原来20px)
    font-weight: 500 !important;
    text-align: center !important;
    line-height: 1.2 !important;
  }

  // 右侧键盘切换按钮（>符号）
  .keyboard-toggle-btn {
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 32px !important;
    height: 32px !important;
    border: none !important;
    background: transparent !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    .chevron-right {
      font-size: 44px !important; // 增加8px (原来36px)
      color: #00ffff !important;
    }
  }

  // 语音按钮（中间按钮）
  .voice-btn {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 2px solid #00bcd4;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

    // 录音状态：变大并且有动态光晕
    &.recording {
      width: 140px;
      height: 140px;
      border-color: #00ffff;
      background: rgba(0, 255, 255, 0.1);
      animation: voiceGlow 2s ease-in-out infinite;
    }

    .voice-button-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      transition: all 0.3s ease;

      &.recording {
        background: rgba(0, 255, 255, 0.2);
      }
    }

    .voice-mic-icon {
      width: 48px;
      height: 48px;
      filter: brightness(0) saturate(100%) invert(77%) sepia(93%) saturate(1352%) hue-rotate(169deg) brightness(97%)
        contrast(96%);
      position: relative;
      z-index: 2;
    }
  }
}

// 键盘输入模式 - 使用更高的特异性来覆盖 Vant 样式
.voice-chat-dialog .keyboard-input-mode {
  display: flex !important;
  align-items: center !important;
  width: 100% !important;
  gap: 16px !important;

  .text-input-container {
    flex: 1 !important;
    display: flex !important;
    align-items: center !important;

    .text-input {
      width: 100% !important;
      height: 56px !important;
      background: rgba(0, 188, 212, 0.05) !important;
      border: 2px solid rgba(255, 255, 255, 0.2) !important;
      border-radius: 16px !important;
      padding: 0 20px !important;
      color: rgba(255, 255, 255, 0.9) !important;
      font-size: 28px !important; // 增加8px (原来20px)
      max-height: 165px !important;
      font-weight: 600 !important;
      outline: none !important;
      transition: all 0.3s ease !important;
      backdrop-filter: blur(10px) !important;
      box-shadow: 0px 0 8px rgba(0, 255, 255, 0.3) !important;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5) !important;
      }

      &:focus {
        border-color: rgba(255, 255, 255, 0.3) !important;
        background: rgba(0, 188, 212, 0.08) !important;
        box-shadow:
          -4px 0 8px rgba(0, 255, 255, 0.4),
          0 0 0 4px rgba(0, 188, 212, 0.1) !important;
      }
    }
  }

  .voice-toggle-btn-circle {
    width: 56px !important;
    height: 56px !important;
    border-radius: 50% !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    background: rgba(0, 188, 212, 0.05) !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0px 0 8px rgba(0, 255, 255, 0.3) !important;
    flex-shrink: 0 !important;

    &:hover {
      background: rgba(0, 188, 212, 0.08);
      border-color: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }

    &:active {
      transform: scale(0.95);
    }

    .voice-button-bg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      border-radius: 50%;
      transition: all 0.3s ease;
    }

    .voice-mic-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%)
        contrast(100%);
      position: relative;
      z-index: 2;
    }
  }

  .send-btn-circle {
    width: 56px !important;
    height: 56px !important;
    border-radius: 50% !important;
    border: 2px solid rgba(255, 255, 255, 0.2) !important;
    background: rgba(0, 188, 212, 0.05) !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    backdrop-filter: blur(10px) !important;
    box-shadow: 0px 0 8px rgba(0, 255, 255, 0.3) !important;
    flex-shrink: 0 !important;

    &:hover:not(.not-input) {
      background: rgba(0, 188, 212, 0.08);
      border-color: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }

    &.not-input {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
      background: rgba(255, 255, 255, 0.05);
      box-shadow: 0px 0 8px rgba(255, 255, 255, 0.1);
    }

    .iconfont {
      font-size: 32px; // 增加8px (原来24px)
      color: #00ffff;
    }

    &:active:not(.not-input) {
      transform: scale(0.95);
    }
  }
}

// 语音按钮光晕动画
@keyframes voiceGlow {
  0% {
    box-shadow:
      0 0 0 0 rgba(0, 255, 255, 0.4),
      0 4px 12px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow:
      0 0 0 15px rgba(0, 255, 255, 0.1),
      0 4px 12px rgba(0, 255, 255, 0.3);
  }
  100% {
    box-shadow:
      0 0 0 30px rgba(0, 255, 255, 0),
      0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

// 删除确认对话框样式
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1100;
  animation: fadeIn 0.3s ease-out;
}

.dialog-container {
  background: rgba(1, 28, 32, 0.6);
  border: none;
  border-radius: 16px;
  padding: 30px;
  box-sizing: border-box;
  backdrop-filter: blur(10px);
  border-left: 4px solid #00ffff;
  box-shadow: -4px 0 8px rgba(0, 255, 255, 0.3);
  transition: all 0.3s ease;
  width: 90%;
  max-width: 500px;
  color: white;
  animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  display: flex;
  flex-direction: column;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
  padding-bottom: 18px;
  position: relative;

  .dialog-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 40px; // 增加8px (原来32px)
    font-weight: 600;
  }

  .dialog-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      border-color: rgba(255, 255, 255, 0.4);
      transform: scale(1.1);
    }

    .close-icon {
      width: 24px;
      height: 24px;
      filter: brightness(0) invert(1);
    }
  }
}

.dialog-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;

  .delete-warning {
    color: #ffffff;
    font-size: 40px; // 增加8px (原来32px)
    font-weight: 600;
    line-height: 1.4;
    margin: 0 0 12px 0;
    text-align: center;

    strong {
      color: #ef4444;
    }
  }

  .delete-hint {
    color: rgba(255, 255, 255, 0.7);
    font-size: 32px; // 增加8px (原来24px)
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
  }
}

.dialog-footer {
  display: flex;
  gap: 20px;
  margin-top: 40px;
  padding-top: 32px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);

  .cancel-btn,
  .confirm-btn,
  .delete-confirm-btn {
    flex: 1;
    padding: 16px 16px;
    border-radius: 20px;
    font-size: 36px; // 增加8px (原来28px)
    font-weight: 600;
    cursor: pointer;
    border: 2px solid;
    background: transparent;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    width: 200px;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .cancel-btn {
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.8);

    &:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(255, 255, 255, 0.3);
    }
  }

  .confirm-btn {
    color: #00bcd4;
    border-color: #00bcd4;

    &:hover:not(:disabled) {
      background: rgba(0, 188, 212, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 188, 212, 0.3);
    }
  }

  .delete-confirm-btn {
    color: #ef4444;
    border-color: #ef4444;

    &:hover:not(:disabled) {
      background: rgba(239, 68, 68, 0.1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    }
  }
}

// 编辑模式样式
.edit-mode {
  .edit-textarea {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 14px 16px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 32px; // 增加8px (原来24px)
    box-sizing: border-box;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
    line-height: 1.6;

    &::placeholder {
      color: rgba(255, 255, 255, 0.5);
    }

    &:focus {
      outline: none;
      border-color: rgba(255, 255, 255, 0.5);
      background: rgba(255, 255, 255, 0.15);
      box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
    }
  }
}

.edit-attribute-container {
  .add-attribute-container {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    align-items: center;
    width: 100%;
    box-sizing: border-box;

    .attribute-value {
      flex: 1;
      min-width: 0;
      max-width: 100%;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      padding: 14px 16px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 32px; // 增加8px (原来24px)
      box-sizing: border-box;
      backdrop-filter: blur(10px);
      transition: all 0.2s ease;

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.5);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
      }
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 弹窗出现动画
@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
